# -*- coding: utf-8 -*-
"""
NIPT数据散点图分析 - 使用相对路径
确保本Python文件与NIPT_data.xlsx在同一文件夹中
"""

# 1. 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 2. 设置中文显示和绘图风格
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
plt.style.use('default')  # 使用默认风格，比较清晰
sns.set_palette("husl")  # 设置颜色盘

# 3. 定义数据文件名（相对路径）
data_file = '附件.xlsx'  # 确保这个文件与.py文件在同一目录下

# 4. 检查文件是否存在
if not os.path.exists(data_file):
    print(f"错误：找不到数据文件 '{data_file}'")
    print("请确保：")
    print("1. 数据文件与Python脚本在同一文件夹中")
    print("2. 文件名拼写正确（包括.xlsx扩展名）")
    print(f"当前工作目录：{os.getcwd()}")
    print(f"该目录下的文件：{os.listdir('.')}")
else:
    print(f"找到数据文件：{data_file}")
    
    try:
        # 5. 读取Excel数据
        print("正在读取数据...")
        df = pd.read_excel(data_file)
        print(f"数据读取成功！形状：{df.shape}")
        
        # 6. 显示数据的前几行和列名，帮助确认数据结构
        print("\n数据前5行：")
        print(df.head())
        
        print("\n数据列名：")
        print(df.columns.tolist())
        
        # 7. 数据清洗 - 处理可能的缺失值
        print("\n缺失值统计：")
        print(df.isnull().sum())
        
        # 8. 筛选男胎数据（根据Y染色体浓度判断，Y染色体浓度 > 0 通常表示男胎）
        # 使用实际的列名
        required_columns = ['检测孕周', '孕妇BMI', 'Y染色体浓度']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"警告：缺少必要的列：{missing_columns}")
            print("可用列：", df.columns.tolist())
        else:
            # 筛选男胎数据（Y染色体浓度 > 0）
            male_df = df[df['Y染色体浓度'] > 0].copy()
            print(f"\n男胎数据数量：{len(male_df)}")

            # 9. 删除这三个关键列的缺失值
            male_clean = male_df[required_columns].dropna()
            print(f"清洗后有效数据量：{len(male_clean)}")

            if len(male_clean) > 0:
                # 检查数据类型并查看实际内容
                print(f"检测孕周数据类型: {male_clean['检测孕周'].dtype}")
                print(f"检测孕周前5个值: {male_clean['检测孕周'].head().tolist()}")
                print(f"孕妇BMI数据类型: {male_clean['孕妇BMI'].dtype}")
                print(f"Y染色体浓度数据类型: {male_clean['Y染色体浓度'].dtype}")

                # 尝试提取数字部分（如果是"12周"这样的格式）
                male_clean = male_clean.copy()
                if male_clean['检测孕周'].dtype == 'object':
                    # 尝试提取数字部分
                    male_clean['检测孕周_数值'] = male_clean['检测孕周'].astype(str).str.extract('(\d+)').astype(float)
                    print(f"提取数字后的检测孕周前5个值: {male_clean['检测孕周_数值'].head().tolist()}")
                    # 使用提取的数值列
                    male_clean['检测孕周'] = male_clean['检测孕周_数值']
                else:
                    male_clean['检测孕周'] = pd.to_numeric(male_clean['检测孕周'], errors='coerce')

                # 再次删除转换后的缺失值
                male_clean = male_clean.dropna()
                print(f"数据类型转换后有效数据量：{len(male_clean)}")

                # 10. 创建画布
                plt.figure(figsize=(15, 6))

                # 11. 子图1：Y染色体浓度 vs. 孕周
                plt.subplot(1, 2, 1)
                plt.scatter(male_clean['检测孕周'], male_clean['Y染色体浓度'],
                           alpha=0.6, s=30, edgecolors='w', linewidth=0.5)

                # 添加趋势线
                z = np.polyfit(male_clean['检测孕周'], male_clean['Y染色体浓度'], 1)
                p = np.poly1d(z)
                plt.plot(male_clean['检测孕周'], p(male_clean['检测孕周']),
                        "r--", linewidth=2, label='趋势线')

                plt.xlabel('孕周 (周)')
                plt.ylabel('Y染色体浓度 (%)')
                plt.title('Y染色体浓度 vs. 孕周 (男胎)')
                plt.grid(True, alpha=0.3)

                # 显示相关系数和方程
                corr = male_clean['Y染色体浓度'].corr(male_clean['检测孕周'])
                plt.text(0.05, 0.95, f'相关系数 R = {corr:.3f}\ny = {float(z[0]):.3f}x + {float(z[1]):.3f}',
                        transform=plt.gca().transAxes, fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.2))

                # 12. 子图2：Y染色体浓度 vs. BMI
                plt.subplot(1, 2, 2)
                plt.scatter(male_clean['孕妇BMI'], male_clean['Y染色体浓度'],
                           alpha=0.6, s=30, edgecolors='w', linewidth=0.5)

                # 添加趋势线
                z_bmi = np.polyfit(male_clean['孕妇BMI'], male_clean['Y染色体浓度'], 1)
                p_bmi = np.poly1d(z_bmi)
                plt.plot(male_clean['孕妇BMI'], p_bmi(male_clean['孕妇BMI']),
                        "r--", linewidth=2, label='趋势线')

                plt.xlabel('BMI')
                plt.ylabel('Y染色体浓度 (%)')
                plt.title('Y染色体浓度 vs. BMI (男胎)')
                plt.grid(True, alpha=0.3)

                # 显示相关系数和方程
                corr_bmi = male_clean['Y染色体浓度'].corr(male_clean['孕妇BMI'])
                plt.text(0.05, 0.95, f'相关系数 R = {corr_bmi:.3f}\ny = {float(z_bmi[0]):.3f}x + {float(z_bmi[1]):.3f}',
                        transform=plt.gca().transAxes, fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.2))

                # 13. 调整布局并显示
                plt.tight_layout()
                plt.show()

                # 14. 保存图像（可选）
                save_path = 'scatter_plots.png'
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"\n散点图已保存为：{save_path}")

                # 15. 打印基本统计信息
                print("\n=== 基本统计信息 ===")
                print("孕周统计:")
                print(male_clean['检测孕周'].describe())
                print("\nBMI统计:")
                print(male_clean['孕妇BMI'].describe())
                print("\nY染色体浓度统计:")
                print(male_clean['Y染色体浓度'].describe())
            else:
                print("警告：没有找到有效的男胎数据")
            
    except Exception as e:
        print(f"读取数据时发生错误：{e}")
        print("请检查：")
        print("1. Excel文件是否损坏")
        print("2. 是否安装了openpyxl或xlrd库（pip install openpyxl）")

# 16. 安装必要库的提示（如果尚未安装）
print("\n如果运行失败，请确保已安装以下库：")
print("pip install pandas matplotlib seaborn openpyxl numpy")