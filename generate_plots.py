# -*- coding: utf-8 -*-
"""
NIPT数据散点图分析 - 简化版本
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文显示和绘图风格
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
sns.set_palette("husl")

print("正在读取数据...")
df = pd.read_excel('附件.xlsx')
print(f"数据读取成功！形状：{df.shape}")

# 筛选男胎数据（Y染色体浓度 > 0）
male_df = df[df['Y染色体浓度'] > 0].copy()
print(f"男胎数据数量：{len(male_df)}")

# 准备数据
required_columns = ['检测孕周', '孕妇BMI', 'Y染色体浓度']
male_clean = male_df[required_columns].dropna()

# 转换孕周数据（从"11w+6"格式提取数字）
male_clean = male_clean.copy()
male_clean['检测孕周'] = male_clean['检测孕周'].astype(str).str.extract('(\d+)').astype(float)
male_clean = male_clean.dropna()

print(f"清洗后有效数据量：{len(male_clean)}")

if len(male_clean) > 0:
    # 创建画布
    plt.figure(figsize=(15, 6))
    
    # 子图1：Y染色体浓度 vs. 孕周
    plt.subplot(1, 2, 1)
    plt.scatter(male_clean['检测孕周'], male_clean['Y染色体浓度'], 
               alpha=0.6, s=30, edgecolors='w', linewidth=0.5)
    
    # 添加趋势线
    z = np.polyfit(male_clean['检测孕周'], male_clean['Y染色体浓度'], 1)
    p = np.poly1d(z)
    plt.plot(male_clean['检测孕周'], p(male_clean['检测孕周']), 
            "r--", linewidth=2, label='趋势线')
    
    plt.xlabel('孕周 (周)')
    plt.ylabel('Y染色体浓度 (%)')
    plt.title('Y染色体浓度 vs. 孕周 (男胎)')
    plt.grid(True, alpha=0.3)
    
    # 显示相关系数和方程
    corr = male_clean['Y染色体浓度'].corr(male_clean['检测孕周'])
    plt.text(0.05, 0.95, f'相关系数 R = {corr:.3f}\ny = {float(z[0]):.3f}x + {float(z[1]):.3f}', 
            transform=plt.gca().transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.2))
    
    # 子图2：Y染色体浓度 vs. BMI
    plt.subplot(1, 2, 2)
    plt.scatter(male_clean['孕妇BMI'], male_clean['Y染色体浓度'], 
               alpha=0.6, s=30, edgecolors='w', linewidth=0.5)
    
    # 添加趋势线
    z_bmi = np.polyfit(male_clean['孕妇BMI'], male_clean['Y染色体浓度'], 1)
    p_bmi = np.poly1d(z_bmi)
    plt.plot(male_clean['孕妇BMI'], p_bmi(male_clean['孕妇BMI']), 
            "r--", linewidth=2, label='趋势线')
    
    plt.xlabel('BMI')
    plt.ylabel('Y染色体浓度 (%)')
    plt.title('Y染色体浓度 vs. BMI (男胎)')
    plt.grid(True, alpha=0.3)
    
    # 显示相关系数和方程
    corr_bmi = male_clean['Y染色体浓度'].corr(male_clean['孕妇BMI'])
    plt.text(0.05, 0.95, f'相关系数 R = {corr_bmi:.3f}\ny = {float(z_bmi[0]):.3f}x + {float(z_bmi[1]):.3f}', 
            transform=plt.gca().transAxes, fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.2))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图像
    save_path = 'scatter_plots.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"\n散点图已保存为：{save_path}")
    
    # 不显示图像，只保存
    # plt.show()
    
    # 打印基本统计信息
    print("\n=== 基本统计信息 ===")
    print("孕周统计:")
    print(male_clean['检测孕周'].describe())
    print("\nBMI统计:")
    print(male_clean['孕妇BMI'].describe())
    print("\nY染色体浓度统计:")
    print(male_clean['Y染色体浓度'].describe())
    
    print(f"\n=== 相关性分析 ===")
    print(f"Y染色体浓度与孕周的相关系数: {corr:.3f}")
    print(f"Y染色体浓度与BMI的相关系数: {corr_bmi:.3f}")
    
else:
    print("警告：没有找到有效的男胎数据")

print("\n分析完成！")
